// Fragments of Sandra - Interactive Comic Engine
class ComicEngine {
    constructor() {
        this.comicData = null;
        this.currentPanelIndex = 0;
        this.storyState = []; // Tracks which variant is selected for each panel
        this.isTransitioning = false;
        
        // DOM elements
        this.panelContainer = document.getElementById('panelContainer');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.panelCounter = document.getElementById('panelCounter');
        this.realityIndicator = document.getElementById('realityIndicator');
        this.glitchOverlay = document.getElementById('glitchOverlay');
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadComicData();
            this.initializeStoryState();
            this.setupEventListeners();
            this.renderCurrentPanel();
        } catch (error) {
            console.error('Failed to initialize comic:', error);
            this.showError('Failed to load comic data');
        }
    }
    
    async loadComicData() {
        const response = await fetch('./comic.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        this.comicData = await response.json();
    }
    
    initializeStoryState() {
        // Initialize with random variants for each panel
        this.storyState = this.comicData.panels.map(panel => {
            if (panel.variants.length === 1) {
                return 0; // Only one variant (like panel 12)
            }
            return Math.floor(Math.random() * panel.variants.length);
        });
    }
    
    setupEventListeners() {
        this.prevBtn.addEventListener('click', () => this.goToPrevious());
        this.nextBtn.addEventListener('click', () => this.goToNext());
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.isTransitioning) return;
            
            if (e.key === 'ArrowLeft' && !this.prevBtn.disabled) {
                this.goToPrevious();
            } else if (e.key === 'ArrowRight' && !this.nextBtn.disabled) {
                this.goToNext();
            }
        });
    }
    
    async goToPrevious() {
        if (this.currentPanelIndex <= 0 || this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        // Trigger glitch effect
        await this.triggerGlitchTransition();
        
        // Re-randomize current and future panels for branching storylines
        this.randomizeFromCurrentPanel();
        
        this.currentPanelIndex--;
        this.renderCurrentPanel();
        this.updateNavigation();
        
        this.isTransitioning = false;
    }
    
    async goToNext() {
        if (this.currentPanelIndex >= this.comicData.panels.length - 1 || this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        // Trigger glitch effect for forward navigation too
        await this.triggerGlitchTransition();
        
        this.currentPanelIndex++;
        this.renderCurrentPanel();
        this.updateNavigation();
        
        this.isTransitioning = false;
    }
    
    randomizeFromCurrentPanel() {
        // Re-randomize variants from current panel onwards
        for (let i = this.currentPanelIndex; i < this.comicData.panels.length; i++) {
            const panel = this.comicData.panels[i];
            if (panel.variants.length > 1) {
                this.storyState[i] = Math.floor(Math.random() * panel.variants.length);
            }
        }
    }
    
    async triggerGlitchTransition() {
        return new Promise(resolve => {
            this.glitchOverlay.classList.add('active');
            
            setTimeout(() => {
                this.glitchOverlay.classList.remove('active');
                resolve();
            }, 500);
        });
    }
    
    renderCurrentPanel() {
        const panel = this.comicData.panels[this.currentPanelIndex];
        const variantIndex = this.storyState[this.currentPanelIndex];
        const variant = panel.variants[variantIndex];
        
        // Clear previous content
        this.panelContainer.innerHTML = '';
        
        // Create panel element
        const panelElement = document.createElement('div');
        panelElement.className = 'panel glitch-in';
        
        // Create image element
        const imageElement = document.createElement('img');
        imageElement.className = 'panel-image';
        imageElement.src = variant.image;
        imageElement.alt = variant.caption || 'Comic panel';
        
        // Handle image load error with placeholder
        imageElement.onerror = () => {
            imageElement.src = this.createPlaceholderImage(variant);
        };
        
        panelElement.appendChild(imageElement);
        
        // Create text container
        if (variant.dialogue || variant.caption) {
            const textContainer = document.createElement('div');
            textContainer.className = 'panel-text';
            
            if (variant.dialogue) {
                const dialogueElement = document.createElement('div');
                dialogueElement.className = 'panel-dialogue';
                
                let dialogueText = variant.dialogue;
                if (variant.speaker) {
                    dialogueText = `${variant.speaker}: "${dialogueText}"`;
                }
                if (variant.response) {
                    dialogueText += `\n\n${variant.responseBy}: "${variant.response}"`;
                }
                
                dialogueElement.textContent = dialogueText;
                textContainer.appendChild(dialogueElement);
            }
            
            if (variant.caption) {
                const captionElement = document.createElement('div');
                captionElement.className = 'panel-caption';
                captionElement.textContent = variant.caption;
                textContainer.appendChild(captionElement);
            }
            
            panelElement.appendChild(textContainer);
        }
        
        this.panelContainer.appendChild(panelElement);
        
        // Update UI indicators
        this.updatePanelInfo(panel, variant);
    }
    
    createPlaceholderImage(variant) {
        // Create a data URL for a placeholder image with text
        const canvas = document.createElement('canvas');
        canvas.width = 600;
        canvas.height = 400;
        const ctx = canvas.getContext('2d');
        
        // Dark cyberpunk background
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Neon border
        ctx.strokeStyle = '#00ffff';
        ctx.lineWidth = 4;
        ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
        
        // Text
        ctx.fillStyle = '#ffffff';
        ctx.font = '20px Orbitron, monospace';
        ctx.textAlign = 'center';
        ctx.fillText('PANEL IMAGE', canvas.width / 2, canvas.height / 2 - 40);
        
        ctx.font = '14px Rajdhani, sans-serif';
        ctx.fillStyle = '#cccccc';
        const lines = this.wrapText(ctx, variant.caption || 'Loading...', canvas.width - 40);
        lines.forEach((line, index) => {
            ctx.fillText(line, canvas.width / 2, canvas.height / 2 + 20 + (index * 20));
        });
        
        return canvas.toDataURL();
    }
    
    wrapText(ctx, text, maxWidth) {
        const words = text.split(' ');
        const lines = [];
        let currentLine = words[0];
        
        for (let i = 1; i < words.length; i++) {
            const word = words[i];
            const width = ctx.measureText(currentLine + ' ' + word).width;
            if (width < maxWidth) {
                currentLine += ' ' + word;
            } else {
                lines.push(currentLine);
                currentLine = word;
            }
        }
        lines.push(currentLine);
        return lines;
    }
    
    updatePanelInfo(panel, variant) {
        this.panelCounter.textContent = `Panel ${panel.number} of ${this.comicData.panels.length}`;
        
        let realityText = 'Reality: ';
        if (variant.reality === 'SHARED') {
            realityText += 'Convergence';
        } else if (variant.reality) {
            realityText += variant.reality;
        } else {
            realityText += '?';
        }
        this.realityIndicator.textContent = realityText;
    }
    
    updateNavigation() {
        // Update previous button
        this.prevBtn.disabled = this.currentPanelIndex <= 0;
        
        // Update next button
        this.nextBtn.disabled = this.currentPanelIndex >= this.comicData.panels.length - 1;
        
        // Update button text based on context
        if (this.currentPanelIndex >= this.comicData.panels.length - 1) {
            this.nextBtn.querySelector('.btn-text').textContent = 'End';
            this.nextBtn.querySelector('.btn-subtext').textContent = 'Story Complete';
        } else {
            this.nextBtn.querySelector('.btn-text').textContent = 'Next';
            this.nextBtn.querySelector('.btn-subtext').textContent = 'Continue';
        }
    }
    
    showError(message) {
        this.panelContainer.innerHTML = `
            <div class="panel">
                <div class="panel-text">
                    <div class="panel-dialogue" style="color: #ff0080;">
                        Error: ${message}
                    </div>
                    <div class="panel-caption">
                        Please check that all files are in the correct location.
                    </div>
                </div>
            </div>
        `;
    }
}

// Initialize the comic when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ComicEngine();
});
